<resources>
    <string name="switch_screen_saver">Turn off the screen saver</string>
    <string name="switch_angle_correction">Use angle correction for angled exposure</string>
    <string name="select_language">Select language</string>
    <string-array name="language_names">
    <item><PERSON><PERSON>š<PERSON></item>
    <item>English</item>
    </string-array>
    <string-array name="language_codes">
    <item>cs</item>
    <item>en</item>
    </string-array>

    <string name="volume">Volume</string>

    <string name="start">Start</string>
    <string name="pause">Pause</string>
    <string name="resume">Resume</string>
    <string name="stop">Stop</string>
    <string name="reset">Reset</string>

    <string name="app_name">Stopky</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="nav_header_title">RTG Studio</string>
    <string name="nav_header_subtitle"><EMAIL></string>
    <string name="nav_header_desc">Navigation header</string>
    <string name="action_settings">Settings</string>

    <string name="menu_home">Home</string>
    <string name="menu_gallery">Gallery</string>
    <string name="menu_slideshow">Slideshow</string>
    <string name="menu_exposurecalculation">Exposure time</string>
    <string name="menu_iqiselection">IQI Gauge Selection</string>
    <string name="menu_wireiqi">Wire IQI</string>
    <string name="menu_exposurecorrection">Exposure correction</string>
    <string name="menu_decay">Decay</string>
    <string name="menu_unsharp">Geometric unsharpness</string>
    <string name="menu_stopwatch">Stopwatch</string>
    <string name="menu_countdown">Countdown</string>
    <string name="menu_matcalc">Calculator</string>
    <string name="menu_settings">Settings</string>
    <string name="title_settings">Settings</string>

    <string name="exposure_density_title">Exposure correction by film density</string>
    <string name="label_exposure1">Exposure 1 [s]</string>
    <string name="label_density1">Film density 1</string>
    <string name="label_density2">Film density 2</string>
    <string name="label_exposure2">Exposure 2 [s]</string>

    <string name="distance_correction_title">Exposure correction by distance</string>
    <string name="label_exposure1_dist">Exposure 1 [s]</string>
    <string name="label_distance1">Distance 1 [mm]</string>
    <string name="label_distance2">Distance 2 [mm]</string>
    <string name="label_exposure2_dist">Exposure 2 [s]</string>

    <string name="formula_density_new">Exp₂ [s] = Exp₁ [s] × (density₂ / density₁)</string>
    <string name="formula_distance_new">Exp₂ [s] = Exp₁ [s] × (Distance₂[mm] / Distance₁ [mm])²</string>

    <string name="hint_exp1">Exp 1</string>
    <string name="hint_dens1">Dens 1</string>
    <string name="hint_dens2">Dens 2</string>
    <string name="hint_exp2">Exp 2</string>
    <string name="hint_dist1">Dist 1</string>
    <string name="hint_dist2">Dist 2</string>

    <string name="label_isotope_type">Isotope type</string>
    <string name="label_initial_activity">Initial activity [%s]</string>
    <string name="label_start_date">Start date</string>
    <string name="hint_select_date">Select date</string>
    <string name="label_target_date">Target date</string>
    <string name="label_current_activity">Current activity [%s]</string>
    <string name="label_target_activity">Activity at target date [%s]</string>

    <string name="unsharp_scheme_desc">Geometric unsharpness diagram</string>
    <string name="label_focal_spot_size">Focal size (F) [%s]</string>
    <string name="label_distance_source_subject">Source–object distance (a) [%s]</string>
    <string name="label_distance_subject_film">Object–film distance (b) [%s]</string>
    <string name="label_geometric_unsharpness">Geometric unsharpness (UG) [%s]</string>

    <string name="label_activity"> Activity </string>
    <string name="label_activity_with_unit">Activity [%s]</string>
    <string name="label_initial_date">Start date</string>
    <string name="label_material">Material</string>
    <string name="label_mu">μ coefficient</string>
    <string name="label_film_type">Film type</string>
    <string name="label_density">Required density</string>
    <string name="label_user_factor">User factor</string>
    <string name="label_thickness">Material thickness</string>
    <string name="label_weld_reinforcement">Weld reinforcement</string>
    <string name="label_distance">Distance isotope</string>
    <string name="label_object_type">Weld type</string>
    <string name="label_barricade_section">Barricade</string>
    <string name="label_num_exposures">Number of exposures</string>
    <string name="label_barricade_dose">Barricade dose</string>
    <string name="label_barricade_dose_with_unit">Dose [%s]</string>
    <string name="label_hvl_collimator">HVL collimator</string>
    <string name="label_exposure_result">(hh:mm:ss)</string>
    <string name="label_stopwatch">Stopwatch</string>
    <string name="label_start">Start</string>
    <string name="label_pause">Pause</string>
    <string name="label_reset">Reset</string>
    <string name="button_clear_history">Clear History</string>
    <string name="label_standard">Standard</string>
    <string name="label_emissivity">Emissivity [R/hr/Ci/ft²]</string>
    <string name="label_k_gamma_constant">K-Gamma constant</string>
    <string name="label_hvl_type">HVL Type</string>
    <string name="label_hvl_value">HVL Value</string>
    <string name="label_unit_system">Unit system</string>
    <string name="label_activity_unit">Activity unit</string>
    <string name="label_dose_unit">Dose unit</string>

    <string name="label_barricade_history">Barricade Calculation History</string>
    <string name="error_film_data_missing">Film data missing</string>
    <string name="label_hvl_collimator_type">HVL Collimator Type</string>
    <string name="label_hvl_collimator_custom_instruction">Custom Collimator (HVL)</string>
    <string name="hint_hvl_value_custom">Enter number of HVL</string>
    <string name="object_type_weld_on_sheet_metal">Weld on sheet metal (perpendicular)</string>
    <string name="object_type_weld_on_a_pipe">Weld on pipe (perpendicular, 2× wall)</string>
    <string name="object_type_solid_round_bar">Solid material (perpendicular, ½ exposure)</string>
    <string name="object_type_angled_exposure">Angled exposure (ellipse, ~70°)</string>

    <string name="error_prefix">Error</string>
    <string name="error_mu_value_not_found">MU value not found</string>
    <string name="error_mu_value_invalid">Invalid MU value</string>
    <string name="error_exposure_const_missing">Exposure constant missing</string>
    <string name="error_activity_invalid">Invalid activity</string>
    <string name="error_half_life_missing">Half-life missing</string>
    <string name="error_initial_date_in_future">Initial date cannot be in the future</string>
    <string name="no_data_available">No data available</string>
    <string name="error_distance_invalid">Invalid distance</string>
    <string name="error_invalid_barricade_inputs">Invalid barricade inputs</string>
    <string name="error_exposure_time_invalid">Invalid exposure time</string>
    <string name="error_calculation">Calculation error</string>
    <string name="error_isotope_not_selected">Isotope not selected</string>
    <string name="error_calculation_negative_sqrt">Calculation error: Negative value under square root</string>

    <!-- IQI Selection strings -->
    <string name="iqi_selection_title">IQI Gauge Selection</string>
    <string name="iqi_input_parameters">Input Parameters</string>
    <string name="iqi_material_label">Material:</string>
    <string name="iqi_thickness_label">Object thickness (mm):</string>
    <string name="iqi_thickness_hint">Enter thickness in mm</string>
    <string name="iqi_testing_class_label">Required testing class:</string>
    <string name="iqi_type_label">Gauge type:</string>
    <string name="iqi_standard_label">Standard for IQI selection:</string>
    <string name="iqi_technique_label">Radiographic technique:</string>
    <string name="iqi_placement_label">Gauge placement:</string>
    <string name="iqi_result_title">Result</string>

    <!-- Spinner options -->
    <string name="select_material">Select material</string>
    <string name="select_class">Select class</string>
    <string name="select_type">Select type</string>
    <string name="select_standard">Select standard</string>
    <string name="select_technique">Select technique</string>
    <string name="select_placement">Select placement</string>
    <string name="loading">Loading...</string>
    <string name="select_standard_first">Select standard first</string>
    <string name="loading_error">Loading error</string>

    <!-- Materials -->
    <string name="material_steel">Steel</string>
    <string name="material_iron">Iron materials</string>
    <string name="material_aluminum">Aluminum</string>
    <string name="material_aluminium">Aluminium</string>
    <string name="material_aluminum_alloys">Aluminum and its alloys</string>
    <string name="material_copper">Copper</string>
    <string name="material_zinc">Zinc</string>
    <string name="material_tin_alloys">Tin and their alloys</string>
    <string name="material_titanium">Titanium</string>
    <string name="material_titan">Titan</string>
    <string name="material_titanium_alloys">Titanium and its alloys</string>
    <string name="material_concrete">Concrete</string>
    <string name="material_wolfram">Tungsten</string>
    <string name="material_lead">Lead</string>
    <string name="material_brass">Brass</string>
    <string name="material_inconel_x">Inconel X</string>
    <string name="material_zirkonium">Zirconium</string>
    <string name="material_uran">Uranium</string>

    <!-- Testing classes -->
    <string name="class_a">A</string>
    <string name="class_b">B</string>

    <!-- IQI types -->
    <string name="iqi_type_wire">Wire</string>
    <string name="iqi_type_step_hole">Step/hole</string>

    <!-- Techniques -->
    <string name="technique_single_wall">Single wall</string>
    <string name="technique_single_wall_iso">Single wall radiography</string>
    <string name="technique_double_wall">Double wall</string>

    <!-- Placements -->
    <string name="placement_source_side">Source side</string>
    <string name="placement_source_side_iso">Radiation source side</string>
    <string name="placement_film_side">Film side</string>

    <!-- Results -->
    <string name="iqi_required_gauge">Required IQI gauge: %s</string>
    <string name="iqi_required_gauge_film">⚠️ Required IQI gauge: %s (FILM SIDE!)</string>
    <string name="iqi_material_code">Gauge material: %s</string>
    <string name="iqi_recommended_set">Recommended gauge: %s</string>
    <string name="iqi_no_set_found">Warning: No suitable gauge found for %s</string>
    <string name="iqi_set_optimal_range">%s (optimal range)</string>
    <string name="iqi_set_available_suboptimal">%s (available, but outside optimal range)</string>
    <string name="result_standard">Standard: %s</string>
    <string name="result_class">Class: %s</string>
    <string name="result_type">Type: %s</string>
    <string name="result_technique">Technique: %s</string>
    <string name="result_placement">Placement: %s</string>

    <!-- Warnings and messages -->
    <string name="film_side_warning">⚠️ WARNING: Film side placement should only be used exceptionally!\nLetter \'F\' is added to the gauge designation.</string>
    <string name="film_side_explanation">⚠️ IMPORTANT: Letter \'F\' indicates film side placement.\nThis placement should only be used exceptionally when source side placement is not possible!</string>

    <!-- Debug and error messages -->
    <string name="error_initialization">Initialization error: %s</string>
    <string name="error_loading_data">Data loading error: %s</string>
    <string name="error_parsing_json">JSON parsing error: %s</string>
    <string name="error_general">General error: %s</string>
    <string name="error_updating_spinners">Spinner update error: %s</string>
    <string name="error_ui_initialization">UI initialization error: %s</string>
    <string name="error_ui_elements_not_found">Some UI elements were not found in layout</string>
    <string name="error_json_empty">JSON file is empty</string>
    <string name="error_json_missing_key">Missing key \'iqi_selection_data\' in JSON</string>
    <string name="error_material_not_found">Object material not found in gauge mapping</string>
    <string name="error_standard_not_found">Specified standard not found</string>
    <string name="error_thickness_not_found">Gauge value not found for specified thickness</string>
    <string name="error_data_processing">Data processing error: %s</string>
    <string name="error_data_not_loaded">Data not loaded</string>
    <string name="debug_table_search">No table found for:\nType: %1$s\nClass: %2$s\nTechnique: %3$s\nPlacement: %4$s</string>
    <string name="error_table_not_found">No relevant table found for specified parameters</string>

    <!-- ANR FIX: New strings for loading and error states -->
    <string name="loading_data">Loading data...</string>
    <string name="error_data_load_failed">Error loading data</string>

    <!-- CardView titles -->
    <string name="cardview_title_isotope_material">⚛️ Isotope and Material Settings</string>
    <string name="cardview_title_film_coefficients">🎞️ Film and Coefficients Settings</string>
    <string name="cardview_title_barricade">🛡️ Barricade Settings and Calculation</string>

    <!-- Content descriptions -->
    <string name="content_desc_expand_collapse">Expand/collapse</string>

    <!-- Barricade section -->
    <string name="label_calculated_barricade_distance">Calculated barricade distance:</string>

    <!-- Unit warning -->
    <string name="warning_check_unit_settings">⚠️ Check unit settings in Settings!</string>

    <!-- Wire IQI Sensitivity strings -->
    <string name="wire_iqi_title">🔍 Wire IQI Sensitivity</string>
    <string name="wire_iqi_wall_question">Single or Double Wall Thickness Wire IQI Sensitivity?</string>
    <string name="wire_iqi_single_wall">Single Wall</string>
    <string name="wire_iqi_double_wall">Double Wall</string>
    <string name="wire_iqi_wire_visible">Wire Visible</string>
    <string name="wire_iqi_material_thickness">Mat. Thickness</string>
    <string name="wire_iqi_sensitivity">Sensitivity</string>
    <string name="wire_iqi_thickness_hint">Thickness (mm)</string>

    <!-- Isotope size settings -->
    <string name="label_isotope_size">Isotope size [%s]</string>
    <string name="hint_IzotopeSize">Enter size</string>

    <string name="unsharp_title">Geometric unsharpness</string>

    <!-- Minimum distance calculation strings -->
    <string name="min_distance_title">Source-object distance</string>
    <string name="label_isotope_size_d">Isotope size (d) [%s]</string>
    <string name="label_testing_class">Testing class</string>
    <string name="label_object_film_distance_b">Object-film distance (b) [%s]</string>
    <string name="label_min_distance_result">Minimum distance (f) [%s]</string>
    <string name="label_total_distance_result">Total distance (f+b) [%s]</string>
    <string name="hint_isotope_size">d</string>
    <string name="hint_object_film_distance">b</string>

</resources>