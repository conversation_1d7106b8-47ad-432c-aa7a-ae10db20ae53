package com.example.stopky.ui.unsharp

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import com.example.stopky.R
import android.content.Context
import android.content.SharedPreferences
import java.util.Locale

class UnsharpFragment : Fragment(), SharedPreferences.OnSharedPreferenceChangeListener {

    private lateinit var inputF: EditText
    private lateinit var inputA: EditText
    private lateinit var inputB: EditText
    private lateinit var textResult: TextView

    // TextView labely pro dynamické jednotky
    private lateinit var labelFocalSpotSize: TextView
    private lateinit var labelDistanceSourceSubject: TextView
    private lateinit var labelDistanceSubjectFilm: TextView
    private lateinit var labelGeometricUnsharpness: TextView

    // TextWatcher pro sledování změn
    private lateinit var textWatcher: TextWatcher

    // Flag pro dočasné zakázání TextWatcher
    private var isUpdatingValues = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_unsharp, container, false)
        inputF = view.findViewById(R.id.inputF)
        inputA = view.findViewById(R.id.inputA)
        inputB = view.findViewById(R.id.inputB)
        textResult = view.findViewById(R.id.textResult)

        // Inicializace TextView labelů
        labelFocalSpotSize = view.findViewById(R.id.labelFocalSpotSize)
        labelDistanceSourceSubject = view.findViewById(R.id.labelDistanceSourceSubject)
        labelDistanceSubjectFilm = view.findViewById(R.id.labelDistanceSubjectFilm)
        labelGeometricUnsharpness = view.findViewById(R.id.labelGeometricUnsharpness)

        // Aktualizace jednotek v labelech
        updateLabels()

        // Inicializace TextWatcher
        textWatcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!isUpdatingValues) {
                    saveInputsInMm()
                    calculateResult()
                }
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }

        inputF.addTextChangedListener(textWatcher)
        inputA.addTextChangedListener(textWatcher)
        inputB.addTextChangedListener(textWatcher)

        // Nastavení rozbalitelné karty
        setupExpandableCard()

        // Načtení uložených hodnot (jednoduché načtení z SharedPreferences)
        loadValues()

        // OPRAVA: Spustit výpočet po načtení uložených hodnot
        calculateResult()

        return view
    }

    override fun onResume() {
        super.onResume()
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        prefs.registerOnSharedPreferenceChangeListener(this)

        // Detekce změny jednotek při návratu do fragmentu
        checkAndHandleUnitChange()
    }

    override fun onPause() {
        super.onPause()
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        prefs.unregisterOnSharedPreferenceChangeListener(this)
    }

    // Implementace SharedPreferences listeneru
    override fun onSharedPreferenceChanged(sharedPreferences: SharedPreferences?, key: String?) {
        if (key == "unit_system") {
            updateLabels()
            clearAllValues()
        }
    }

    private fun updateLabels() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unit = when (unitSystem) {
            "Metrický" -> "mm"
            "Americký", "Britský" -> "inch"
            else -> "mm"
        }

        labelFocalSpotSize.text = getString(R.string.label_focal_spot_size, unit)
        labelDistanceSourceSubject.text = getString(R.string.label_distance_source_subject, unit)
        labelDistanceSubjectFilm.text = getString(R.string.label_distance_subject_film, unit)
        labelGeometricUnsharpness.text = getString(R.string.label_geometric_unsharpness, unit)
    }

    private fun clearAllValues() {
        inputF.setText("")
        inputA.setText("")
        inputB.setText("")
        textResult.text = "0.00"
    }

    private fun checkAndHandleUnitChange() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val currentUnitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unsharpPrefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        val lastUnitSystem = unsharpPrefs.getString("last_unit_system", "") ?: ""

        // Aktualizace labelů vždy
        updateLabels()

        // Pokud se jednotkový systém změnil, vymaž hodnoty
        if (lastUnitSystem.isNotEmpty() && lastUnitSystem != currentUnitSystem) {
            clearAllValues()
        }

        // Uložení aktuálního jednotkového systému
        unsharpPrefs.edit().putString("last_unit_system", currentUnitSystem).apply()
    }

    private fun saveInputsInMm() {
        if (isUpdatingValues) return

        // Jednoduché uložení hodnot do SharedPreferences (bez konverze)
        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString("inputF", inputF.text.toString())
            .putString("inputA", inputA.text.toString())
            .putString("inputB", inputB.text.toString())
            .apply()
    }

    private fun loadValues() {
        isUpdatingValues = true

        // Jednoduché načtení hodnot ze SharedPreferences
        val prefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)
        inputF.setText(prefs.getString("inputF", ""))
        inputA.setText(prefs.getString("inputA", ""))
        inputB.setText(prefs.getString("inputB", ""))
        textResult.text = prefs.getString("result", "0.00")

        isUpdatingValues = false
    }



    private fun calculateResult() {
        val prefs = PreferenceManager.getDefaultSharedPreferences(requireContext())
        val unitSystem = prefs.getString("unit_system", "Metrický") ?: "Metrický"
        val unsharpPrefs = requireContext().getSharedPreferences("unsharp_prefs", Context.MODE_PRIVATE)

        // Načtení aktuálních hodnot z EditText polí
        val f = inputF.text.toString().replace(',', '.').toDoubleOrNull()
        val a = inputA.text.toString().replace(',', '.').toDoubleOrNull()
        val b = inputB.text.toString().replace(',', '.').toDoubleOrNull()

        if (f != null && a != null && b != null && a != 0.0) {
            // Konverze vstupních hodnot na mm pro výpočet
            val fMm = convertToMm(f, unitSystem)
            val aMm = convertToMm(a, unitSystem)
            val bMm = convertToMm(b, unitSystem)

            // Výpočet v mm
            val resultMm = (fMm * bMm) / aMm

            // Konverze výsledku do aktuálního jednotkového systému
            val resultInCurrentUnit = convertFromMm(resultMm, unitSystem)

            val resultText = formatValue(resultInCurrentUnit, unitSystem)
            textResult.text = resultText

            // Uložit výsledek do SharedPreferences
            unsharpPrefs.edit().putString("result", resultText).apply()
        } else {
            textResult.text = "0.00"
            unsharpPrefs.edit().putString("result", "0.00").apply()
        }
    }

    // Jednoduché lokální funkce pro konverzi
    private fun convertToMm(value: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> value
            "Americký", "Britský" -> value * 25.4
            else -> value
        }
    }

    private fun convertFromMm(valueInMm: Double, unitSystem: String): Double {
        return when (unitSystem) {
            "Metrický" -> valueInMm
            "Americký", "Britský" -> valueInMm / 25.4
            else -> valueInMm
        }
    }

    private fun formatValue(value: Double, unitSystem: String): String {
        return when (unitSystem) {
            "Metrický" -> String.format(Locale.US, "%.1f", value)
            "Americký", "Britský" -> String.format(Locale.US, "%.3f", value)
            else -> String.format(Locale.US, "%.1f", value)
        }
    }

}